<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pendidikan_karyawan', function (Blueprint $table) {
            // Add unique constraint to prevent duplicate education levels for the same employee
            $table->unique(['karyawan_id', 'tingkat'], 'unique_karyawan_tingkat_pendidikan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pendidikan_karyawan', function (Blueprint $table) {
            $table->dropUnique('unique_karyawan_tingkat_pendidikan');
        });
    }
};
