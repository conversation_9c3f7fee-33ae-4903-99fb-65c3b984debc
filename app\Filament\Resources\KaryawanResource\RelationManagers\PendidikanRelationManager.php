<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class PendidikanRelationManager extends RelationManager
{
    protected static string $relationship = 'pendidikan';
    protected static ?string $title = 'Riwayat Pendidikan';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('tingkat')
                ->label('Tingkat Pendidikan')
                ->options([
                    'SD' => 'SD',
                    'SMP' => 'SMP',
                    'SMA/SMK' => 'SMA/SMK',
                    'D1' => 'D1',
                    'D2' => 'D2',
                    'D3' => 'D3',
                    'S1' => 'S1',
                    'S2' => 'S2',
                    'S3' => 'S3',
                ])
                ->required()
                ->rules([
                    function () {
                        return function (string $attribute, $value, \Closure $fail) {
                            $karyawanId = $this->getOwnerRecord()->id;
                            $recordId = $this->getRecord()?->id;

                            $exists = \App\Models\PendidikanKaryawan::where('karyawan_id', $karyawanId)
                                ->where('tingkat', $value)
                                ->when($recordId, function ($query) use ($recordId) {
                                    return $query->where('id', '!=', $recordId);
                                })
                                ->exists();

                            if ($exists) {
                                $fail('Tingkat pendidikan ini sudah ada untuk karyawan ini.');
                            }
                        };
                    },
                ])
                ->helperText('Setiap karyawan hanya boleh memiliki satu tingkat pendidikan yang sama.'),
            Forms\Components\TextInput::make('institusi')->required(),
            Forms\Components\TextInput::make('jurusan')->required(),
            // max this year
            Forms\Components\TextInput::make('tahun_lulus')->numeric()->nullable()->maxValue(date('Y')),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('tingkat'),
            Tables\Columns\TextColumn::make('institusi'),
            Tables\Columns\TextColumn::make('jurusan'),
            Tables\Columns\TextColumn::make('tahun_lulus'),
        ])
            ->defaultSort('tahun_lulus', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([Tables\Actions\CreateAction::make()]);
    }
}
