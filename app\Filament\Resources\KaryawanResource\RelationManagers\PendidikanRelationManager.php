<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class PendidikanRelationManager extends RelationManager
{
    protected static string $relationship = 'pendidikan';
    protected static ?string $title = 'Riwayat Pendidikan';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('tingkat')
                ->label('Tingkat Pendidikan')
                ->options([
                    'SD' => 'SD',
                    'SMP' => 'SMP',
                    'SMA/SMK' => 'SMA/SMK',
                    'D1' => 'D1',
                    'D2' => 'D2',
                    'D3' => 'D3',
                    'S1' => 'S1',
                    'S2' => 'S2',
                    'S3' => 'S3',
                ])
                ->required()

                ->helperText('Setiap karyawan hanya boleh memiliki satu tingkat pendidikan yang sama.'),
            Forms\Components\TextInput::make('institusi')->required(),
            Forms\Components\TextInput::make('jurusan')->required(),
            // max this year
            Forms\Components\TextInput::make('tahun_lulus')->numeric()->nullable()->maxValue(date('Y')),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('tingkat'),
            Tables\Columns\TextColumn::make('institusi'),
            Tables\Columns\TextColumn::make('jurusan'),
            Tables\Columns\TextColumn::make('tahun_lulus'),
        ])
            ->defaultSort('tahun_lulus', 'desc')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $this->validateDuplicateEducation($data, $this->mountedTableActionRecord);
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $this->validateDuplicateEducation($data);
                        return $data;
                    })
            ]);
    }

    protected function validateDuplicateEducation(array $data, $excludeId = null): void
    {
        $karyawanId = $this->getOwnerRecord()->id;

        $exists = \App\Models\PendidikanKaryawan::where('karyawan_id', $karyawanId)
            ->where('tingkat', $data['tingkat'])
            ->when($excludeId, function ($query) use ($excludeId) {
                return $query->where('id', '!=', $excludeId);
            })
            ->exists();

        if ($exists) {
            throw new \Exception('Tingkat pendidikan ini sudah ada untuk karyawan ini.');
        }
    }
}
