<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Models\Shift;
use App\Models\Karyawan;
use App\Models\RiwayatKontrak;
use App\Models\PenggajianKaryawan;
use App\Models\PendidikanKaryawan;
use App\Models\KerabatDarurat;
use App\Models\KaryawanBpjs;
use App\Models\KpiPenilaian;
use App\Models\Pelanggaran;
use App\Models\JenisPelanggaran;
use App\Models\AturanKeterlambatan;
use App\Models\PayrollPeriod;
use App\Models\PayrollTransaction;
use App\Models\PayrollDeduction;
use App\Models\Dokumen;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\MutasiPromosiDemosi;
use Carbon\Carbon;
use Faker\Factory as Faker;

class ComprehensiveTestSeeder extends Seeder
{

    private $faker;
    private $entitas = [];
    private $departemen = [];
    private $divisi = [];
    private $jabatan = [];
    private $shifts = [];
    private $users = [];
    private $karyawan = [];
    private $jenisPelanggaran = [];
    private $aturanKeterlambatan = [];
    private $payrollPeriods = [];

    public function run(): void
    {
        $this->faker = Faker::create('id_ID');

        $this->command->info('🚀 Starting Comprehensive Database Reset & Seeding...');
        $this->command->info('⚠️  This will TRUNCATE ALL TABLES and reseed with fresh comprehensive data!');

        try {
            // 1. Truncate all tables
            $this->truncateAllTables();

            // 2. Seed master data (including violations and lateness rules)
            $this->seedMasterData();

            // 3. Seed basic organizational structure
            $this->seedOrganizationalStructure();

            // 4. Seed users (admin, supervisors, employees)
            $this->seedUsers();

            // 5. Seed 300 karyawan
            $this->seedKaryawan();

            // 6. Seed all relation manager data
            $this->seedRelationManagerData();

            // 7. Seed payroll data
            $this->seedPayrollData();

            $this->command->info('✅ Comprehensive Database Seeding completed successfully!');
            $this->printSummary();
        } catch (\Exception $e) {
            $this->command->error('❌ Seeder failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Truncate all tables in correct order
     */
    private function truncateAllTables(): void
    {
        $this->command->info('🗑️  Truncating all tables...');

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Truncate in reverse dependency order
        $tables = [
            'payroll_deductions',
            'payroll_transactions',
            'payroll_periods',
            'absensi',
            'jadwal_kerja',
            'pelanggaran',
            'dokumen',
            'kpi_penilaian',
            'mutasi_promosi_demosi',
            'karyawan_bpjs',
            'kerabat_darurat',
            'pendidikan_karyawan',
            'penggajian_karyawan',
            'riwayat_kontrak',
            'karyawan',
            'aturan_keterlambatan',
            'jenis_pelanggaran',
            'shifts',
            'jabatan',
            'divisi',
            'departemen',
            'entitas',
            'users'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                DB::table($table)->truncate();
                $this->command->info("   ✓ Truncated: {$table}");
            }
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('✅ All tables truncated successfully!');
    }

    /**
     * Seed master data for violations and lateness rules
     */
    private function seedMasterData(): void
    {
        $this->command->info('📊 Seeding master data...');

        $this->seedJenisPelanggaran();
        $this->seedAturanKeterlambatan();

        $this->command->info('✅ Master data seeded successfully!');
    }

    /**
     * Seed jenis pelanggaran
     */
    private function seedJenisPelanggaran(): void
    {
        $this->command->info('⚠️ Seeding jenis pelanggaran...');

        $jenisPelanggaranData = [
            [
                'kode_pelanggaran' => 'PL001',
                'nama_pelanggaran' => 'Terlambat Masuk Kerja',
                'deskripsi' => 'Karyawan terlambat masuk kerja tanpa pemberitahuan',
                'kategori' => 'ringan',
                'denda_nominal' => 25000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL002',
                'nama_pelanggaran' => 'Tidak Menggunakan Seragam',
                'deskripsi' => 'Karyawan tidak menggunakan seragam sesuai ketentuan',
                'kategori' => 'ringan',
                'denda_nominal' => 15000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL003',
                'nama_pelanggaran' => 'Tidak Mengikuti SOP',
                'deskripsi' => 'Karyawan tidak mengikuti Standard Operating Procedure',
                'kategori' => 'sedang',
                'denda_nominal' => 50000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL004',
                'nama_pelanggaran' => 'Alpha Tanpa Keterangan',
                'deskripsi' => 'Karyawan tidak masuk kerja tanpa pemberitahuan',
                'kategori' => 'sedang',
                'denda_nominal' => 1.0,
                'jenis_denda' => 'persentase_gaji',
                'persentase_denda' => 1.0,
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL005',
                'nama_pelanggaran' => 'Melanggar Keamanan Data',
                'deskripsi' => 'Karyawan melanggar protokol keamanan data perusahaan',
                'kategori' => 'berat',
                'denda_nominal' => 2.0,
                'jenis_denda' => 'persentase_gaji',
                'persentase_denda' => 2.0,
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL006',
                'nama_pelanggaran' => 'Konflik dengan Rekan Kerja',
                'deskripsi' => 'Karyawan terlibat konflik yang mengganggu produktivitas',
                'kategori' => 'sedang',
                'denda_nominal' => 75000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL007',
                'nama_pelanggaran' => 'Merokok di Area Terlarang',
                'deskripsi' => 'Karyawan merokok di area yang dilarang merokok',
                'kategori' => 'ringan',
                'denda_nominal' => 20000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
            ],
            [
                'kode_pelanggaran' => 'PL008',
                'nama_pelanggaran' => 'Pelanggaran Disiplin Berat',
                'deskripsi' => 'Pelanggaran disiplin kategori berat sesuai peraturan perusahaan',
                'kategori' => 'berat',
                'denda_nominal' => 2.5,
                'jenis_denda' => 'persentase_gaji',
                'persentase_denda' => 2.5,
                'is_active' => true,
            ],
        ];

        foreach ($jenisPelanggaranData as $data) {
            $jenisPelanggaran = JenisPelanggaran::create($data);
            $this->jenisPelanggaran[] = $jenisPelanggaran;
        }

        $this->command->info("   ✓ Created " . count($this->jenisPelanggaran) . " jenis pelanggaran");
    }

    /**
     * Seed aturan keterlambatan
     */
    private function seedAturanKeterlambatan(): void
    {
        $this->command->info('⏱️ Seeding aturan keterlambatan...');

        $aturanKeterlambatanData = [
            [
                'nama_aturan' => 'Terlambat 1-15 Menit',
                'menit_dari' => 1,
                'menit_sampai' => 15,
                'denda_nominal' => 10000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
                'keterangan' => 'Denda untuk keterlambatan 1-15 menit',
            ],
            [
                'nama_aturan' => 'Terlambat 16-30 Menit',
                'menit_dari' => 16,
                'menit_sampai' => 30,
                'denda_nominal' => 25000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
                'keterangan' => 'Denda untuk keterlambatan 16-30 menit',
            ],
            [
                'nama_aturan' => 'Terlambat 31-60 Menit',
                'menit_dari' => 31,
                'menit_sampai' => 60,
                'denda_nominal' => 50000,
                'jenis_denda' => 'nominal_tetap',
                'is_active' => true,
                'keterangan' => 'Denda untuk keterlambatan 31-60 menit',
            ],
            [
                'nama_aturan' => 'Terlambat Lebih dari 60 Menit',
                'menit_dari' => 61,
                'menit_sampai' => null,
                'denda_nominal' => 1000,
                'jenis_denda' => 'per_menit',
                'denda_per_menit' => 1000,
                'is_active' => true,
                'keterangan' => 'Denda Rp 1.000 per menit untuk keterlambatan lebih dari 60 menit',
            ],
        ];

        foreach ($aturanKeterlambatanData as $data) {
            $aturanKeterlambatan = AturanKeterlambatan::create($data);
            $this->aturanKeterlambatan[] = $aturanKeterlambatan;
        }

        $this->command->info("   ✓ Created " . count($this->aturanKeterlambatan) . " aturan keterlambatan");
    }

    private function seedOrganizationalStructure(): void
    {
        $this->command->info('📋 Seeding organizational structure...');

        // Entitas
        $entitasData = [
            ['nama' => 'PT Viera Teknologi Indonesia', 'alamat' => 'Jakarta Pusat', 'keterangan' => 'Kantor Pusat'],
            ['nama' => 'PT Viera Digital Solutions', 'alamat' => 'Jakarta Selatan', 'keterangan' => 'Cabang Jakarta'],
            ['nama' => 'PT Viera Innovation Labs', 'alamat' => 'Bandung', 'keterangan' => 'Cabang Bandung'],
        ];

        foreach ($entitasData as $data) {
            $this->entitas[] = Entitas::create($data);
        }

        // Departemen
        $departemenData = [
            'Technology',
            'Human Resources',
            'Finance',
            'Marketing',
            'Operations',
            'Sales',
            'Customer Service',
            'Research & Development',
            'Quality Assurance',
            'Legal'
        ];

        foreach ($departemenData as $nama) {
            $this->departemen[] = Departemen::create(['nama_departemen' => $nama]);
        }

        // Divisi
        $divisiData = [
            ['nama_divisi' => 'Software Development', 'departemen_id' => $this->departemen[0]->id],
            ['nama_divisi' => 'Infrastructure', 'departemen_id' => $this->departemen[0]->id],
            ['nama_divisi' => 'Data Analytics', 'departemen_id' => $this->departemen[0]->id],
            ['nama_divisi' => 'Recruitment', 'departemen_id' => $this->departemen[1]->id],
            ['nama_divisi' => 'Training & Development', 'departemen_id' => $this->departemen[1]->id],
            ['nama_divisi' => 'Accounting', 'departemen_id' => $this->departemen[2]->id],
            ['nama_divisi' => 'Treasury', 'departemen_id' => $this->departemen[2]->id],
            ['nama_divisi' => 'Digital Marketing', 'departemen_id' => $this->departemen[3]->id],
            ['nama_divisi' => 'Content Creation', 'departemen_id' => $this->departemen[3]->id],
            ['nama_divisi' => 'Business Operations', 'departemen_id' => $this->departemen[4]->id],
        ];

        foreach ($divisiData as $data) {
            $this->divisi[] = Divisi::create($data);
        }

        // Jabatan
        $jabatanData = [
            'Chief Executive Officer',
            'Chief Technology Officer',
            'Chief Financial Officer',
            'Director',
            'General Manager',
            'Manager',
            'Assistant Manager',
            'Team Lead',
            'Senior Developer',
            'Developer',
            'Junior Developer',
            'Intern',
            'Senior Analyst',
            'Analyst',
            'Junior Analyst',
            'Specialist',
            'Senior Consultant',
            'Consultant',
            'Associate',
            'Staff'
        ];

        foreach ($jabatanData as $nama) {
            $this->jabatan[] = Jabatan::create(['nama_jabatan' => $nama]);
        }

        // Shifts
        $shiftData = [
            ['nama_shift' => 'Regular', 'waktu_mulai' => '09:00:00', 'waktu_selesai' => '18:00:00', 'is_active' => true],
            ['nama_shift' => 'Early Bird', 'waktu_mulai' => '07:00:00', 'waktu_selesai' => '16:00:00', 'is_active' => true],
            ['nama_shift' => 'Night Shift', 'waktu_mulai' => '22:00:00', 'waktu_selesai' => '07:00:00', 'is_active' => true],
            ['nama_shift' => 'Flexible', 'waktu_mulai' => '10:00:00', 'waktu_selesai' => '19:00:00', 'is_active' => true],
        ];

        foreach ($shiftData as $data) {
            $this->shifts[] = Shift::create($data);
        }
    }

    private function seedUsers(): void
    {
        $this->command->info('👥 Seeding users...');

        // Admin users
        for ($i = 1; $i <= 5; $i++) {
            $this->users[] = User::create([
                'name' => "Admin User $i",
                'email' => "admin$<EMAIL>",
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);
        }

        // Supervisor users
        for ($i = 1; $i <= 20; $i++) {
            $this->users[] = User::create([
                'name' => "Supervisor $i",
                'email' => "supervisor$<EMAIL>",
                'password' => Hash::make('password'),
                'role' => 'supervisor',
                'email_verified_at' => now(),
            ]);
        }

        // Employee users (will be created with karyawan)
    }

    private function seedKaryawan(): void
    {
        $this->command->info('👨‍💼 Seeding 300 karyawan...');

        $adminUsers = collect($this->users)->where('role', 'admin');
        $supervisorUsers = collect($this->users)->where('role', 'supervisor');

        for ($i = 1; $i <= 300; $i++) {
            // Create user for employee
            $user = User::create([
                'name' => $this->faker->name,
                'email' => "employee$<EMAIL>",
                'password' => Hash::make('password'),
                'role' => 'karyawan',
                'email_verified_at' => now(),
            ]);

            $this->users[] = $user;

            // Create karyawan
            $karyawan = Karyawan::create([
                'nama_lengkap' => $user->name,
                'nip' => 'EMP' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'nik' => $this->faker->unique()->numerify('################'),
                'email' => $user->email,
                'nomor_kk' => $this->faker->numerify('################'),
                'agama' => $this->faker->randomElement(['Islam', 'Kristen', 'Katolik', 'Hindu', 'Buddha']),
                'jenis_kelamin' => $this->faker->randomElement(['Laki-laki', 'Perempuan']),
                'status_pernikahan' => $this->faker->randomElement(['Belum Menikah', 'Menikah', 'Cerai']),
                'jumlah_anak' => $this->faker->numberBetween(0, 4),
                'kota_lahir' => $this->faker->city,
                'tanggal_lahir' => $this->faker->dateTimeBetween('-50 years', '-20 years')->format('Y-m-d'),
                'alamat' => $this->faker->address,
                'alamat_ktp' => $this->faker->address,
                'nama_ibu_kandung' => $this->faker->name('female'),
                'golongan_darah' => $this->faker->randomElement(['A', 'B', 'AB', 'O']),
                'nomor_telepon' => $this->faker->phoneNumber,
                'id_entitas' => $this->faker->randomElement($this->entitas)->id,
                'id_departemen' => $this->faker->randomElement($this->departemen)->id,
                'id_divisi' => $this->faker->randomElement($this->divisi)->id,
                'id_jabatan' => $this->faker->randomElement($this->jabatan)->id,
                'id_user' => $user->id,
                'supervisor_id' => $this->faker->randomElement($supervisorUsers)->id,
                'status_aktif' => $this->faker->boolean(90), // 90% active
                'created_by' => $this->faker->randomElement($adminUsers)->id,
            ]);

            $this->karyawan[] = $karyawan;

            if ($i % 50 == 0) {
                $this->command->info("   Created $i karyawan...");
            }
        }
    }

    private function seedRelationManagerData(): void
    {
        $this->command->info('📊 Seeding relation manager data...');

        $this->seedRiwayatKontrak();
        $this->seedPenggajian();
        $this->seedPendidikan();
        $this->seedKerabatDarurat();
        $this->seedBpjs();
        $this->seedKpiPenilaian();
        $this->seedPelanggaran();
        $this->seedDokumen();
        $this->seedSchedule();
        $this->seedAbsensi();
        $this->seedMutasiPromosiDemosi();
    }

    private function seedRiwayatKontrak(): void
    {
        $this->command->info('📄 Seeding riwayat kontrak...');

        foreach ($this->karyawan as $index => $karyawan) {
            // Each employee has 1-3 contract history records
            $contractCount = $this->faker->numberBetween(1, 3);

            for ($i = 0; $i < $contractCount; $i++) {
                $startDate = $this->faker->dateTimeBetween('-3 years', '-' . ($contractCount - $i) . ' years');
                $contractType = $this->faker->randomElement(['PKWT', 'PKWTT', 'Probation', 'Freelance']);

                RiwayatKontrak::create([
                    'karyawan_id' => $karyawan->id,
                    'jenis_kontrak' => $contractType,
                    'no_kontrak' => 'CTR-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT) . '-' . ($i + 1),
                    'tgl_mulai' => $startDate->format('Y-m-d'),
                    'tanggal_mulai_kerja' => $startDate->format('Y-m-d'),
                    'tgl_selesai' => $contractType === 'PKWTT' ? null :
                        $this->faker->dateTimeBetween($startDate, '+2 years')->format('Y-m-d'),
                    'keterangan' => $this->faker->sentence,
                    'is_active' => $i === ($contractCount - 1) ? 1 : 0, // Latest contract is active
                ]);
            }
        }
    }

    private function seedPenggajian(): void
    {
        $this->command->info('💰 Seeding penggajian...');

        foreach ($this->karyawan as $index => $karyawan) {
            // Each employee has 6-12 months of salary records
            $salaryCount = $this->faker->numberBetween(6, 12);

            for ($i = 0; $i < $salaryCount; $i++) {
                $periode = Carbon::now()->subMonths($salaryCount - $i - 1)->format('Y-m');
                $gajiPokok = $this->faker->numberBetween(5000000, 15000000);
                $tunjanganJabatan = $gajiPokok * 0.2;
                $tunjanganUmum = $this->faker->numberBetween(500000, 1500000);
                $tunjanganSembako = $this->faker->numberBetween(300000, 800000);

                PenggajianKaryawan::create([
                    'karyawan_id' => $karyawan->id,
                    'no_penggajian' => 'PG-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT) . '-' . ($i + 1),
                    'periode_gaji' => $periode,
                    'gaji_pokok' => $gajiPokok,
                    'tunjangan_jabatan' => $tunjanganJabatan,
                    'tunjangan_umum' => $tunjanganUmum,
                    'tunjangan_sembako' => $tunjanganSembako,
                    'bpjs_kesehatan_dipotong' => $gajiPokok * 0.01,
                    'bpjs_tk_dipotong' => $gajiPokok * 0.02,
                    'potongan_lainnya' => $this->faker->numberBetween(0, 500000),
                    'keterangan' => $this->faker->sentence,
                ]);
            }
        }
    }

    private function seedPendidikan(): void
    {
        $this->command->info('🎓 Seeding pendidikan...');

        foreach ($this->karyawan as $karyawan) {
            // Each employee has 1-3 education records
            $educationCount = $this->faker->numberBetween(1, 3);

            $levels = ['SD', 'SMP', 'SMA', 'D3', 'S1', 'S2', 'S3'];
            $usedLevels = []; // Track used levels to prevent duplicates

            for ($i = 0; $i < $educationCount; $i++) {
                // Get available levels (not yet used for this employee)
                $availableLevels = array_diff($levels, $usedLevels);

                if (empty($availableLevels)) {
                    break; // No more unique levels available
                }

                $selectedLevel = $this->faker->randomElement($availableLevels);
                $usedLevels[] = $selectedLevel;

                try {
                    PendidikanKaryawan::create([
                        'karyawan_id' => $karyawan->id,
                        'tingkat' => $selectedLevel,
                        'institusi' => $this->faker->company . ' University',
                        'jurusan' => $this->faker->randomElement([
                            'Teknik Informatika',
                            'Sistem Informasi',
                            'Manajemen',
                            'Akuntansi',
                            'Teknik Elektro',
                            'Psikologi'
                        ]),
                        'tahun_lulus' => $this->faker->year,
                    ]);
                } catch (\Exception $e) {
                    // Skip if duplicate (shouldn't happen with our logic, but just in case)
                    $this->command->warn("Skipped duplicate education level {$selectedLevel} for karyawan {$karyawan->id}");
                }
            }
        }
    }

    private function seedKerabatDarurat(): void
    {
        $this->command->info('👨‍👩‍👧‍👦 Seeding kerabat darurat...');

        foreach ($this->karyawan as $karyawan) {
            // Each employee has 1-2 emergency contacts
            $contactCount = $this->faker->numberBetween(1, 2);

            for ($i = 0; $i < $contactCount; $i++) {
                KerabatDarurat::create([
                    'karyawan_id' => $karyawan->id,
                    'nama_kerabat' => $this->faker->name,
                    'hubungan' => $this->faker->randomElement([
                        'Ayah',
                        'Ibu',
                        'Suami',
                        'Istri',
                        'Anak',
                        'Saudara'
                    ]),
                    'no_hp_kerabat' => $this->faker->phoneNumber,
                    'alamat_kerabat' => $this->faker->address,
                ]);
            }
        }
    }

    private function seedBpjs(): void
    {
        $this->command->info('🏥 Seeding BPJS data...');

        foreach ($this->karyawan as $karyawan) {
            // 80% of employees have BPJS data
            if ($this->faker->boolean(80)) {
                KaryawanBpjs::create([
                    'karyawan_id' => $karyawan->id,
                    'bpjs_kes' => $this->faker->numerify('############'),
                    'bpjs_tk' => $this->faker->numerify('############'),
                    'npwp' => $this->faker->numerify('##.###.###.#-###.###'),
                    'rekening' => $this->faker->bankAccountNumber,
                    'bank' => $this->faker->randomElement(['BCA', 'BNI', 'BRI', 'Mandiri', 'CIMB']),
                ]);
            }
        }
    }

    private function seedKpiPenilaian(): void
    {
        $this->command->info('📊 Seeding KPI penilaian...');

        $supervisorUsers = collect($this->users)->where('role', 'supervisor');
        $adminUsers = collect($this->users)->where('role', 'admin');
        $penilaiUsers = $supervisorUsers->merge($adminUsers);

        foreach ($this->karyawan as $karyawan) {
            // Each employee has 3-6 KPI assessments
            $kpiCount = $this->faker->numberBetween(3, 6);

            for ($i = 0; $i < $kpiCount; $i++) {
                $periode = Carbon::now()->subMonths($kpiCount - $i - 1)->format('Y-m');
                $targetKpi = $this->faker->numberBetween(70, 100);
                $realisasiKpi = $this->faker->numberBetween(60, 120);

                // Determine nilai_akhir based on achievement
                $achievement = ($realisasiKpi / $targetKpi) * 100;
                $nilaiAkhir = 'D';
                if ($achievement >= 90) $nilaiAkhir = 'A';
                elseif ($achievement >= 80) $nilaiAkhir = 'B';
                elseif ($achievement >= 70) $nilaiAkhir = 'C';

                KpiPenilaian::create([
                    'karyawan_id' => $karyawan->id,
                    'periode' => $periode,
                    'target_kpi' => $targetKpi,
                    'realisasi_kpi' => $realisasiKpi,
                    'nilai_akhir' => $nilaiAkhir,
                    'status_penilaian' => $this->faker->randomElement(['Draft', 'Proses', 'Selesai']),
                    'penilai_id' => $this->faker->randomElement($penilaiUsers)->id,
                    'tanggal_penilaian' => $this->faker->dateTimeThisMonth->format('Y-m-d'),
                    'kategori_penilaian' => json_encode([
                        'kualitas_kerja' => $this->faker->numberBetween(1, 5),
                        'kuantitas_kerja' => $this->faker->numberBetween(1, 5),
                        'ketepatan_waktu' => $this->faker->numberBetween(1, 5),
                        'kerjasama_tim' => $this->faker->numberBetween(1, 5),
                    ]),
                    'keterangan' => $this->faker->sentence,
                    'created_by' => $this->faker->randomElement($penilaiUsers)->id,
                ]);
            }
        }
    }

    private function seedPelanggaran(): void
    {
        $this->command->info('⚠️ Seeding pelanggaran...');

        $adminUsers = collect($this->users)->where('role', 'admin');
        $supervisorUsers = collect($this->users)->where('role', 'supervisor');
        $penilaiUsers = $supervisorUsers->merge($adminUsers);

        foreach ($this->karyawan as $karyawan) {
            // 30% of employees have violations
            if ($this->faker->boolean(30)) {
                $violationCount = $this->faker->numberBetween(1, 4);

                for ($i = 0; $i < $violationCount; $i++) {
                    $jenisPelanggaran = $this->faker->randomElement($this->jenisPelanggaran);

                    Pelanggaran::create([
                        'karyawan_id' => $karyawan->id,
                        'jenis_pelanggaran_id' => $jenisPelanggaran->id,
                        'tanggal' => $this->faker->dateTimeBetween('-3 months', 'now')->format('Y-m-d'),
                        'keterangan' => $jenisPelanggaran->deskripsi . ' - ' . $this->faker->sentence,
                        'created_by' => $this->faker->randomElement($penilaiUsers)->id,
                    ]);
                }
            }
        }
    }

    private function seedDokumen(): void
    {
        $this->command->info('📁 Seeding dokumen...');

        foreach ($this->karyawan as $karyawan) {
            // Each employee has 2-5 documents
            $docCount = $this->faker->numberBetween(2, 5);

            for ($i = 0; $i < $docCount; $i++) {
                Dokumen::create([
                    'karyawan_id' => $karyawan->id,
                    'nama_dokumen' => $this->faker->randomElement([
                        'CV',
                        'KTP',
                        'Ijazah',
                        'Sertifikat',
                        'Kontrak Kerja',
                        'NPWP',
                        'BPJS',
                        'Foto',
                        'Surat Lamaran'
                    ]),
                    'file_path' => 'documents/' . $this->faker->uuid . '.pdf',
                ]);
            }
        }
    }

    private function seedSchedule(): void
    {
        $this->command->info('📅 Seeding schedule...');

        foreach ($this->karyawan as $karyawan) {
            // Create schedules for the last 30 days
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::now()->subDays($i);

                // Skip weekends for some employees
                if ($date->isWeekend() && $this->faker->boolean(70)) {
                    continue;
                }

                Schedule::create([
                    'karyawan_id' => $karyawan->id,
                    'shift_id' => $this->faker->randomElement($this->shifts)->id,
                    'tanggal_jadwal' => $date->format('Y-m-d'),
                    'waktu_masuk' => $this->faker->time('H:i:s'),
                    'waktu_keluar' => $this->faker->time('H:i:s'),
                    'status' => $this->faker->randomElement(['aktif', 'libur', 'cuti']),
                    'keterangan' => $this->faker->boolean(20) ? $this->faker->sentence : null,
                ]);
            }
        }
    }

    private function seedAbsensi(): void
    {
        $this->command->info('⏰ Seeding absensi...');

        foreach ($this->karyawan as $karyawan) {
            // Create attendance for the last 30 days
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::now()->subDays($i);

                // Skip weekends for most employees
                if ($date->isWeekend() && $this->faker->boolean(80)) {
                    continue;
                }

                // 90% attendance rate
                if ($this->faker->boolean(90)) {
                    $jamMasuk = $this->faker->time('H:i:s', '09:30:00'); // Some come late
                    $jamKeluar = $this->faker->time('H:i:s', '18:30:00');

                    // Determine status based on time
                    $status = 'hadir';
                    if ($jamMasuk > '09:00:00') {
                        $status = 'terlambat';
                    }

                    Absensi::create([
                        'karyawan_id' => $karyawan->id,
                        'tanggal_absensi' => $date->format('Y-m-d'),
                        'waktu_masuk' => $jamMasuk,
                        'waktu_keluar' => $jamKeluar,
                        'status' => $status,
                        'latitude_masuk' => $this->faker->latitude(-6.2, -6.1), // Jakarta area
                        'longitude_masuk' => $this->faker->longitude(106.8, 106.9),
                        'latitude_keluar' => $this->faker->latitude(-6.2, -6.1),
                        'longitude_keluar' => $this->faker->longitude(106.8, 106.9),
                        'foto_masuk' => 'attendance/' . $this->faker->uuid . '.jpg',
                        'foto_keluar' => 'attendance/' . $this->faker->uuid . '.jpg',
                        'keterangan' => $this->faker->boolean(10) ? $this->faker->sentence : null,
                    ]);
                } else {
                    // Absent with reason
                    Absensi::create([
                        'karyawan_id' => $karyawan->id,
                        'tanggal_absensi' => $date->format('Y-m-d'),
                        'status' => $this->faker->randomElement(['izin', 'sakit', 'cuti', 'alpha']),
                        'keterangan' => $this->faker->sentence,
                    ]);
                }
            }
        }
    }

    private function seedMutasiPromosiDemosi(): void
    {
        $this->command->info('🔄 Seeding mutasi promosi demosi...');

        foreach ($this->karyawan as $karyawan) {
            // 20% of employees have mutation/promotion history
            if ($this->faker->boolean(20)) {
                $mutationCount = $this->faker->numberBetween(1, 2);

                for ($i = 0; $i < $mutationCount; $i++) {
                    MutasiPromosiDemosi::create([
                        'karyawan_id' => $karyawan->id,
                        'tipe' => $this->faker->randomElement(['promosi', 'demosi', 'mutasi', 'posisi_awal']),
                        'entitas_id' => $this->faker->randomElement($this->entitas)->id,
                        'departemen_id' => $this->faker->randomElement($this->departemen)->id,
                        'divisi_id' => $this->faker->randomElement($this->divisi)->id,
                        'jabatan_id' => $this->faker->randomElement($this->jabatan)->id,
                        'tanggal_efektif' => $this->faker->dateTimeThisYear->format('Y-m-d'),
                        'alasan' => $this->faker->sentence,
                        'is_active' => $this->faker->boolean(20), // 20% chance to be active
                    ]);
                }
            }
        }
    }

    /**
     * Seed payroll data
     */
    private function seedPayrollData(): void
    {
        $this->command->info('💰 Seeding payroll data...');

        $this->seedPayrollPeriods();
        $this->seedPayrollTransactions();

        $this->command->info('✅ Payroll data seeded successfully!');
    }

    /**
     * Seed payroll periods
     */
    private function seedPayrollPeriods(): void
    {
        $this->command->info('📊 Seeding payroll periods...');

        $periods = [
            [
                'nama_periode' => 'Payroll Januari 2025',
                'tanggal_mulai' => '2025-01-01',
                'tanggal_selesai' => '2025-01-31',
                'tanggal_cutoff' => '2025-01-31',
                'status' => 'completed',
                'keterangan' => 'Periode payroll bulan Januari 2025',
            ],
            [
                'nama_periode' => 'Payroll Februari 2025',
                'tanggal_mulai' => '2025-02-01',
                'tanggal_selesai' => '2025-02-28',
                'tanggal_cutoff' => '2025-02-28',
                'status' => 'completed',
                'keterangan' => 'Periode payroll bulan Februari 2025',
            ],
            [
                'nama_periode' => 'Payroll Maret 2025',
                'tanggal_mulai' => '2025-03-01',
                'tanggal_selesai' => '2025-03-31',
                'tanggal_cutoff' => '2025-03-31',
                'status' => 'processing',
                'keterangan' => 'Periode payroll bulan Maret 2025',
            ],
        ];

        foreach ($periods as $periodData) {
            $period = PayrollPeriod::create($periodData);
            $this->payrollPeriods[] = $period;
        }

        $this->command->info("   ✓ Created " . count($this->payrollPeriods) . " payroll periods");
    }

    /**
     * Seed payroll transactions
     */
    private function seedPayrollTransactions(): void
    {
        $this->command->info('💸 Seeding payroll transactions...');

        $adminUsers = collect($this->users)->where('role', 'admin');
        $totalTransactions = 0;

        // Create transactions for completed periods only
        $completedPeriods = collect($this->payrollPeriods)->where('status', 'completed');

        foreach ($completedPeriods as $period) {
            foreach ($this->karyawan as $index => $karyawan) {
                // Get latest salary data for this employee
                $latestSalary = PenggajianKaryawan::where('karyawan_id', $karyawan->id)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if (!$latestSalary) {
                    continue; // Skip if no salary data
                }

                // Calculate attendance data for this period
                $attendanceData = $this->calculateAttendanceForPeriod($karyawan->id, $period);

                // Calculate deductions
                $potonganKeterlambatan = $this->calculateLatenessDeduction($attendanceData['total_menit_terlambat']);
                $potonganPelanggaran = $this->calculateViolationDeduction($karyawan->id, $period, $latestSalary->gaji_pokok);

                $totalGajiKotor = $latestSalary->gaji_pokok +
                    $latestSalary->tunjangan_jabatan +
                    $latestSalary->tunjangan_umum +
                    $latestSalary->tunjangan_sembako;

                $totalPotongan = $latestSalary->bpjs_kesehatan_dipotong +
                    $latestSalary->bpjs_tk_dipotong +
                    $potonganKeterlambatan +
                    $potonganPelanggaran;

                $takeHomePay = $totalGajiKotor - $totalPotongan;

                PayrollTransaction::create([
                    'no_payroll' => 'PAY-' . $period->id . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                    'payroll_period_id' => $period->id,
                    'karyawan_id' => $karyawan->id,
                    'penggajian_karyawan_id' => $latestSalary->id,
                    'gaji_pokok' => $latestSalary->gaji_pokok,
                    'tunjangan_jabatan' => $latestSalary->tunjangan_jabatan,
                    'tunjangan_umum' => $latestSalary->tunjangan_umum,
                    'tunjangan_sembako' => $latestSalary->tunjangan_sembako,
                    'total_gaji_kotor' => $totalGajiKotor,
                    'potongan_bpjs_kesehatan' => $latestSalary->bpjs_kesehatan_dipotong,
                    'potongan_bpjs_tk' => $latestSalary->bpjs_tk_dipotong,
                    'potongan_keterlambatan' => $potonganKeterlambatan,
                    'potongan_pelanggaran' => $potonganPelanggaran,
                    'potongan_lainnya' => 0,
                    'total_potongan' => $totalPotongan,
                    'take_home_pay' => $takeHomePay,
                    'total_hari_kerja' => $attendanceData['total_hari_kerja'],
                    'total_hari_hadir' => $attendanceData['total_hari_hadir'],
                    'total_menit_terlambat' => $attendanceData['total_menit_terlambat'],
                    'total_pelanggaran' => $attendanceData['total_pelanggaran'],
                    'status' => 'approved',
                    'keterangan' => "Payroll {$period->nama_periode}",
                    'created_by' => $this->faker->randomElement($adminUsers)->id,
                    'approved_by' => $this->faker->randomElement($adminUsers)->id,
                    'approved_at' => now(),
                ]);

                $totalTransactions++;
            }
        }

        $this->command->info("   ✓ Created {$totalTransactions} payroll transactions");
    }

    /**
     * Calculate attendance data for a specific period
     */
    private function calculateAttendanceForPeriod($karyawanId, $period)
    {
        $startDate = Carbon::parse($period->tanggal_mulai);
        $endDate = Carbon::parse($period->tanggal_selesai);

        $totalHariKerja = 0;
        $totalHariHadir = 0;
        $totalMenitTerlambat = 0;
        $totalPelanggaran = 0;

        // Count working days (excluding weekends)
        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            if (!$currentDate->isWeekend()) {
                $totalHariKerja++;
            }
            $currentDate->addDay();
        }

        // Count actual attendance
        $absensiRecords = Absensi::where('karyawan_id', $karyawanId)
            ->whereBetween('tanggal_absensi', [$startDate, $endDate])
            ->get();

        foreach ($absensiRecords as $absensi) {
            if (in_array($absensi->status, ['hadir', 'terlambat'])) {
                $totalHariHadir++;

                if ($absensi->status === 'terlambat' && $absensi->waktu_masuk) {
                    $scheduledTime = Carbon::parse($absensi->tanggal_absensi . ' 08:00:00');
                    $actualTime = Carbon::parse($absensi->waktu_masuk);
                    $minutesLate = $actualTime->diffInMinutes($scheduledTime);
                    $totalMenitTerlambat += $minutesLate;
                }
            }
        }

        // Count violations in this period
        $totalPelanggaran = Pelanggaran::where('karyawan_id', $karyawanId)
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->count();

        return [
            'total_hari_kerja' => $totalHariKerja,
            'total_hari_hadir' => $totalHariHadir,
            'total_menit_terlambat' => $totalMenitTerlambat,
            'total_pelanggaran' => $totalPelanggaran,
        ];
    }

    /**
     * Calculate lateness deduction based on total minutes late
     */
    private function calculateLatenessDeduction($totalMinutesLate)
    {
        if ($totalMinutesLate <= 0) {
            return 0;
        }

        $totalDeduction = 0;

        foreach ($this->aturanKeterlambatan as $aturan) {
            if ($aturan->isApplicable($totalMinutesLate)) {
                $totalDeduction += $aturan->hitungDenda($totalMinutesLate);
                break; // Use the first applicable rule
            }
        }

        return $totalDeduction;
    }

    /**
     * Calculate violation deduction for a specific period
     */
    private function calculateViolationDeduction($karyawanId, $period, $gajiPokok)
    {
        $startDate = Carbon::parse($period->tanggal_mulai);
        $endDate = Carbon::parse($period->tanggal_selesai);

        $violations = Pelanggaran::where('karyawan_id', $karyawanId)
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->with('jenisPelanggaran')
            ->get();

        $totalDeduction = 0;

        foreach ($violations as $violation) {
            if ($violation->jenisPelanggaran) {
                $totalDeduction += $violation->jenisPelanggaran->hitungDenda($gajiPokok);
            }
        }

        return $totalDeduction;
    }

    private function printSummary(): void
    {
        $this->command->info('📊 SEEDING SUMMARY:');
        $this->command->info('==================');

        $summary = [
            'Users' => count($this->users),
            'Entitas' => count($this->entitas),
            'Departemen' => count($this->departemen),
            'Divisi' => count($this->divisi),
            'Jabatan' => count($this->jabatan),
            'Shifts' => count($this->shifts),
            'Jenis Pelanggaran' => count($this->jenisPelanggaran),
            'Aturan Keterlambatan' => count($this->aturanKeterlambatan),
            'Karyawan' => count($this->karyawan),
            'Riwayat Kontrak' => RiwayatKontrak::count(),
            'Penggajian' => PenggajianKaryawan::count(),
            'Pendidikan' => PendidikanKaryawan::count(),
            'Kerabat Darurat' => KerabatDarurat::count(),
            'BPJS Data' => KaryawanBpjs::count(),
            'KPI Penilaian' => KpiPenilaian::count(),
            'Pelanggaran' => Pelanggaran::count(),
            'Dokumen' => Dokumen::count(),
            'Schedule' => Schedule::count(),
            'Absensi' => Absensi::count(),
            'Mutasi/Promosi' => MutasiPromosiDemosi::count(),
            'Payroll Periods' => count($this->payrollPeriods),
            'Payroll Transactions' => PayrollTransaction::count(),
        ];

        foreach ($summary as $item => $count) {
            $this->command->info("✅ $item: $count records");
        }

        $this->command->info('==================');
        $this->command->info('🎉 Total records created: ' . array_sum($summary));
        $this->command->info('🚀 System ready for performance testing!');
    }
}
